package com.holderzone.saas.store.member;

import com.holderzone.saas.store.member.domain.IntegralRuleDO;
import com.holderzone.saas.store.member.domain.MemberCardDO;
import com.holderzone.saas.store.member.domain.TransactionRecordDO;
import com.holderzone.saas.store.member.mapper.IntegralRuleMapper;
import com.holderzone.saas.store.member.mapper.TransactionRecordMapper;
import com.holderzone.saas.store.member.service.impl.MemberServiceImpl;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

/*@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreMemberApplication.class)
@WebAppConfiguration
@AutoConfigureMockMvc
public class MemberTests {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private TransactionRecordMapper transactionRecordMapper;

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private MemberTransactionServiceImpl ms;

    @Autowired
    private MemberService memberService;
    @Autowired
    private ApplicationContext ap;
    @Autowired
    private MemberListService mls;

    @Autowired
    private IntegralRuleMapper integralRuleMapper;
    @Autowired
    private EntServiceClient entServiceClient;
    @Before
    public void setupMockMvc() throws Exception {
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    @Autowired
    DynamicHelper helper;
    @Test
    public void creat() throws Exception {
        MemberDTO memberDTO = new MemberDTO();
        memberDTO.setName("赵宏洋3");
        String str = "2017-11-21";
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(str, fmt);
        memberDTO.setBirthday(date);
        memberDTO.setPhone("13504741160");
        memberDTO.setSex((byte) 1);
        memberDTO.setPassword("123");
        memberDTO.setStoreGuid("fa10a446-1869-4da7-b322-3c012470b5c1");
//        String writeValueAsString = JacksonUtils.writeValueAsString(memberDTO);
        String writeValueAsString = JSON.toJSONString(memberDTO);
        mockMvc.perform(post("/member/create").contentType(MediaType.APPLICATION_JSON).content(writeValueAsString)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void testUpdateMember() throws Exception {
       MemberDTO memberDTO = new MemberDTO();
        memberDTO.setPhone("18667903181");
        memberDTO.setPassword("123");
        String s = JSON.toJSONString(memberDTO);
        String contentAsString = mockMvc.perform(post("/member/get_member").contentType(MediaType.APPLICATION_JSON).content(s)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        JSONObject jsonObject = JSON.parseObject(contentAsString);

       memberDTO = new MemberDTO();
        memberDTO.setName("赵宏洋2");
        String str = "1992-11-21";
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(str, fmt);
        memberDTO.setMemberGuid("6440052854038378501");
        memberDTO.setBirthday(date);
        memberDTO.setPhone("18667903182");
        memberDTO.setSex((byte) 1);
        memberDTO.setPassword("123");
        memberDTO.setStoreGuid("fa10a446-1869-4da7-b322-3c012470b5c1");
//        String writeValueAsString = JacksonUtils.writeValueAsString(memberDTO);
        String writeValueAsString = JSON.toJSONString(memberDTO);
        mockMvc.perform(post("/member/update_member").contentType(MediaType.APPLICATION_JSON).content(writeValueAsString)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void testGetMember() throws Exception {
        BaseMemberDTO memberDTO=new BaseMemberDTO();
        memberDTO.setPhone("15828377291");
        String s = JSON.toJSONString(memberDTO);
        mockMvc.perform(post("/member/get_member").contentType(MediaType.APPLICATION_JSON).content(s)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void testForgetPassWd() throws Exception {
        mockMvc.perform(post("/member/forget_password").contentType(MediaType.MULTIPART_FORM_DATA).param("phone", "18667903181")).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void ins() throws Exception {
        for (int i = 0; i < 15 ;i++) {
            TransactionRecordDO transactionRecordDO = new TransactionRecordDO();
            transactionRecordDO.setBalance(new BigDecimal(100));
            transactionRecordDO.setMemberGuid("6448000295317632001");
            transactionRecordDO.setSequenceNo(IDUtils.nextId());
            transactionRecordDO.setTransactionRecordGuid(helper.generateGuid(RedisConstant.TRANSACTION_RECORD_GUID));
            transactionRecordDO.setFee(new BigDecimal(i));
            transactionRecordDO.setType((byte)0);
            transactionRecordDO.setPrepaidType((byte)2);
            transactionRecordMapper.insertSelective(transactionRecordDO);
        }

    }

    @Test
    public void SelectMemberList() throws Exception {
        MemberListReqDTO memberListReqDTO = new MemberListReqDTO();
        memberListReqDTO.setCurrentPage(1);
        memberListReqDTO.setPageSize(2);
        memberListReqDTO.setSearchType(1);
        memberListReqDTO.setSex(1);
        memberListReqDTO.setBirthdayBegin(LocalDate.parse("1991-12-13"));
        //DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        memberListReqDTO.setGmtCreateBegin(LocalDateTime.parse("2018-08-28T11:52:29"));


        String writeValueAsString = JacksonUtils.writeValueAsString(memberListReqDTO);
        String contentAsString = mockMvc.perform(post("/member_list/getMemberList").contentType(MediaType.APPLICATION_JSON).content
                (writeValueAsString)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        System.out.println(contentAsString);
    }


    @Test
    public void SelectMemberDetail() throws Exception {
        BaseMemberDTO memberDTO=new BaseMemberDTO();
        memberDTO.setMemberGuid("6448000295317632001");
        System.out.println(JacksonUtils.writeValueAsString(memberDTO));
//
       *//* String writeValueAsString = JSON.toJSONString(memberDTO);
        String contentAsString = mockMvc.perform(post("/member_list/memberDetail").contentType(MediaType.APPLICATION_JSON).content
                (writeValueAsString)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        System.out.println(contentAsString);*//*
    }


    @Test
    public void SelectMemberCards() throws Exception {
        MemberCardsReqDTO mc=new MemberCardsReqDTO();
        mc.setMemberGuid("6447658480962870280");
        mc.setNum("112233445566778999");
        String writeValueAsString = JSON.toJSONString(mc);
        String contentAsString = mockMvc.perform(post("/member_list/memberCards").contentType(MediaType.APPLICATION_JSON).content
                (writeValueAsString)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        System.out.println(contentAsString);
    }

    @Test
    public void SelectMemberGradeList() throws Exception{
        BaseDTO baseDTO=new BaseDTO();
        String writeValueAsString = JSON.toJSONString(baseDTO);
        String contentAsString = mockMvc.perform(post("/member_list/memberGrades").contentType(MediaType.APPLICATION_JSON).content
                (writeValueAsString)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        System.out.println(contentAsString);

    }



    @Test
    public void AddTransactions() throws Exception{
        MemberNotifyDTO memberNotifyDTO=new MemberNotifyDTO();
        memberNotifyDTO.setAmount(new BigDecimal(1000));
        memberNotifyDTO.setConsumeType(0);
        memberNotifyDTO.setIntegral(50);
        memberNotifyDTO.setType(1);
        memberNotifyDTO.setUseIntegral(true);
        memberNotifyDTO.setMemberGuid("6447658480962870280");
        memberNotifyDTO.setSequenceNo("gagzaqq");
        memberNotifyDTO.setOrderGuid("qqqr11");
        memberNotifyDTO.setOperationStaffGuid("123");
        memberNotifyDTO.setOperationStaffName("张三");
        ms.addTransactionRecord(memberNotifyDTO);


    }

    @Test
    public void GetMemberTest() throws Exception{

        MemberLoginRespDTO memberLoginResByPhone = memberService.getMemberLoginResByPhone("17882334428");
        System.out.println(memberLoginResByPhone);

    }

    @Test
    public void TestRandomGuid(){


        helper.changeRedis("6461877249663747073");

        System.out.println(helper.generateGuid(RedisConstant.MEMBER_GUID));
    }

    @Test
    public void TestInsertNull(){
        IntegralRuleDO io3 = new IntegralRuleDO();
        io3.setAllowBalance(new Integer(0).byteValue());
        io3.setConsumeFeeMin(new BigDecimal(0));
        io3.setConsumeFeeUnit(new BigDecimal(0));
        io3.setConsumeIntegralMax(new Integer(0));
        io3.setConsumeIntegralUnit(0);
        io3.setIsOpen(new Integer(0).byteValue());
        io3.setGmtCreate(LocalDateTime.now());
        io3.setType(new Integer(1).byteValue());
        io3.setMemberGradeGuid("1223445");
        io3.setConsumeType(3);
        io3.setGetType(null);

        io3.setIntegralRuleGuid("123456789");
        integralRuleMapper.insertDefault(io3);
    }

    @Test
    public  void  TestEnt(){
        MessageConfigDTO messageInfo = entServiceClient.getMessageInfo("6462851524036100097");
        System.out.println(messageInfo);
        *//*List<DeductShortMessageDTO> ds=new ArrayList<>();
        DeductShortMessageDTO d=new DeductShortMessageDTO();
        d.setEnterpriseGuid("6461876380421102593");
        d.setDeductCount(1);
        ds.add(d);
        Boolean aBoolean = entServiceClient.deductShortMessage(ds);
        System.out.println(aBoolean);*//*

    }
    @Test
    public void TestInsertDefault(){
       *//* memberService.InitDefaultMemberData();*//*
    }

    @Test
    public void TestLenth(){
      String s="哈";
        System.out.println(s.length());

    }

    @Test
    public void TestRedisTest(){
       MemberCardsReqDTO m=new MemberCardsReqDTO();
       m.setMemberGuid("6464068302958341121");
        List<MemberCardsRespDTO> memberCards = mls.getMemberCards(m);
        System.out.println(memberCards.size());

    }
    @Test
    public void TestSecurityManagement(){
      String md5PassWord =SecurityManager.entryptMd5("123456gl",false);
      boolean t=SecurityManager.decryptMd5("123456gl",md5PassWord,true);
        System.out.println(md5PassWord);
        System.out.println(t);

    }

}*/