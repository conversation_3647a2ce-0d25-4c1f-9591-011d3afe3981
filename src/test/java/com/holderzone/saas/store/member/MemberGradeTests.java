package com.holderzone.saas.store.member;

import com.holderzone.saas.store.member.domain.IntegralRuleDO;
import com.holderzone.saas.store.member.domain.MemberDO;
import com.holderzone.saas.store.member.domain.MemberGradeDO;
import com.holderzone.saas.store.member.mapper.IntegralRuleMapper;
import com.holderzone.saas.store.member.mapper.MemberGradeMapper;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;

/*
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HolderSaasStoreMemberApplication.class)
@WebAppConfiguration
@AutoConfigureMockMvc
public class MemberGradeTests {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RedisTemplate redisTemplate;

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext context;
    @Autowired
    private MemberGradeMapper memberGradeMapper;
    @Autowired
    private IntegralRuleMapper integralRuleMapper;

    @Autowired
    private ApplicationContext ap;
    @Autowired
    DynamicHelper helper;


    @Before
    public void setupMockMvc() throws Exception {
        SpringContextUtils.getInstance().setCfgContext((ConfigurableApplicationContext) ap);
        mockMvc = MockMvcBuilders.webAppContextSetup(context).build();
    }

    @Test
    public void getAccountValidity() throws Exception {
        mockMvc.perform(post("/grade/get_account_validity")).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void updateAccountValidity() throws Exception {
        AccountValidityDTO accountValidityDTO = new AccountValidityDTO();
        accountValidityDTO.setStoreGuid("12334");
        accountValidityDTO.setExpiryType((byte)0);
        accountValidityDTO.setYear(0);
        String writeValueAsString = JSON.toJSONString(accountValidityDTO);
        mockMvc.perform(post("/grade/update_account_validity").contentType(MediaType.APPLICATION_JSON).content(writeValueAsString)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void memberGradeList() throws Exception {

        String contentAsString = mockMvc.perform(post("/grade/member_grade_list")).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
        System.out.println(contentAsString);
       */
/* JSONArray tdata = JSON.parseObject(contentAsString).getJSONArray("tdata");
//        List<MemberGradeDTO> memberGradeDTOS = new ArrayList<>();
//        List<MemberGradeDTO> list = JacksonUtils.toObject(memberGradeDTOS.getClass(), tdata);
//        System.out.println(list);
        MemberGradeDTO memberGradeDTO = tdata.getObject( 5,MemberGradeDTO.class);
        memberGradeDTO.setName("超级小菜鸡最强王者");*//*

//        mockMvc.perform(post("/grade/update_member_grade").contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(memberGradeDTO))).andDo(print())
//                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
//        mockMvc.perform(post("/grade/delete_member_grade").contentType(MediaType.APPLICATION_JSON).content(JacksonUtils.writeValueAsString(memberGradeDTO))).andDo(print())
//                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();

    }

    @Test
    public void addMemberGrade() throws Exception {
        MemberGradeDTO memberGradeDTO = new MemberGradeDTO();
        memberGradeDTO.setName("最强小学生");
        memberGradeDTO.setNeedIntegral(100);
        memberGradeDTO.setIsDefault((byte)0);
        memberGradeDTO.setDiscount(new BigDecimal(1));
        memberGradeDTO.setPrepaidRule((byte)1);
        List<Integer> list = new ArrayList<>();
        list.add(100);
        list.add(200);
        String string = JacksonUtils.writeValueAsString(list);
//        memberGradeDTO.setPrepaidLimit(string);
        memberGradeDTO.setStyle((byte)0);
        memberGradeDTO.setColor(1);
        List<MemberGradeDTO.IntegralRuleDTO> integralRuleDTOS = new ArrayList<>();
        MemberGradeDTO.IntegralRuleDTO integralRuleDTO1 = new MemberGradeDTO.IntegralRuleDTO();
        MemberGradeDTO.IntegralRuleDTO integralRuleDTO2 = new MemberGradeDTO.IntegralRuleDTO();
        MemberGradeDTO.IntegralRuleDTO integralRuleDTO3 = new MemberGradeDTO.IntegralRuleDTO();
        integralRuleDTO1.setType((byte)1);
        integralRuleDTO1.setIsOpen((byte)1);
        integralRuleDTO1.setConsumeFeeMin(new BigDecimal(10));
        integralRuleDTO1.setConsumeIntegralMax(100);
        integralRuleDTO1.setConsumeType(ConsumeIntegralRuleTypeEnum.OFFSET.getCode());
        integralRuleDTO1.setConsumeFeeUnit(new BigDecimal(1));
        integralRuleDTO1.setConsumeIntegralUnit(10);
        integralRuleDTO2.setType((byte)0);
        integralRuleDTO2.setIsOpen((byte)1);
        integralRuleDTO2.setConsumeType(GetIntegralRuleTypeEnum.PREPAID.getCode());
        integralRuleDTO2.setGetFeeUnit(new BigDecimal(1));
        integralRuleDTO2.setGetIntegralUnit(2);
        integralRuleDTO3.setType((byte)0);
        integralRuleDTO3.setIsOpen((byte)1);
        integralRuleDTO3.setConsumeType(GetIntegralRuleTypeEnum.CONSUME.getCode());
        integralRuleDTO3.setGetFeeUnit(new BigDecimal(1));
        integralRuleDTO3.setGetIntegralUnit(2);
        integralRuleDTOS.add(integralRuleDTO1);
        integralRuleDTOS.add(integralRuleDTO2);
        integralRuleDTOS.add(integralRuleDTO3);
        memberGradeDTO.setIntegralRuleDTOS(integralRuleDTOS);
        String writeValueAsString = JSON.toJSONString(memberGradeDTO);
        mockMvc.perform(post("/grade/add_member_grade").contentType(MediaType.APPLICATION_JSON).content(writeValueAsString)).andDo(print())
                .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
    }

    @Test
    public void testDeleteGrade(){
       */
/* 6456415098357240833*//*

       MemberGradeDTO mo=new MemberGradeDTO();
       mo.setMemberGradeGuid("6456415098357240833");
       String writeValueAsString=JSON.toJSONString(mo);
        try {
           String contentAsString=mockMvc.perform(post("/grade/delete_member_grade").contentType(MediaType.APPLICATION_JSON).content(writeValueAsString)).andDo(print())
                    .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
            System.out.println(contentAsString);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testUpdateGrade(){
       */
/* 6456415098357240833*//*

        MemberGradeDTO mo=new MemberGradeDTO();
        mo.setMemberGradeGuid("6462559465364764673");
        mo.setName("须佐能乎");
        mo.setDiscount(new BigDecimal(5));
        mo.setNeedIntegral(100);
        List<String> ps=new ArrayList<>();
        ps.add("10");
        mo.setPrepaidLimit(ps);
        List<MemberGradeDTO.IntegralRuleDTO> io=new ArrayList<>();
        MemberGradeDTO.IntegralRuleDTO io1=new MemberGradeDTO.IntegralRuleDTO();
        io1.setIntegralRuleGuid("6462559516518403073");
        io1.setGetFeeUnit(new BigDecimal(15));
        io.add(io1);
        mo.setIntegralRuleDTOS(io);

        String writeValueAsString=JSON.toJSONString(mo);
        try {
            String contentAsString=mockMvc.perform(post("/grade/update_member_grade").contentType(MediaType.APPLICATION_JSON).content(writeValueAsString)).andDo(print())
                    .andExpect(status().isOk()).andReturn().getResponse().getContentAsString();
            System.out.println(contentAsString);
        } catch (Exception e) {
          
            e.printStackTrace();
        }

    }

    @Test
    public void testInsertDefault(){

        IntegralRuleDO io = new IntegralRuleDO();
       io.setIntegralRuleGuid(helper.generateGuid(RedisConstant.INTEGRAL_RULE_GUID));
    }

    @Test
    public  void testChangeGrade(){
        List<MemberGradeDO> memberGradeDOList = memberGradeMapper.selectAllMemberGrade();
        Collections.sort(memberGradeDOList, new Comparator<MemberGradeDO>() {
            @Override
            public int compare(MemberGradeDO o1, MemberGradeDO o2) {
                return  o1.getNeedIntegral()-o2.getNeedIntegral();
            }
        });
        MemberDO m=new MemberDO();
        m.setTotalIntegral(159);

        Collections.reverse(memberGradeDOList);
        for (int i=0;i<memberGradeDOList.size();i++){
            System.out.println(memberGradeDOList.get(i));
            if (m.getTotalIntegral()>=memberGradeDOList.get(i).getNeedIntegral()){
                m.setMemberGradeGuid(memberGradeDOList.get(i).getMemberGradeGuid());
                break;
            }
        }
        System.out.println(m.getMemberGradeGuid());
    }
    @Test
    public  void TestDateAfter(){
        LocalDateTime t1=LocalDateTime.of(2018,11,5,12,25,30);
        LocalDateTime t2=LocalDateTime.of(2018,11,5,12,25,30);
        System.out.println(t1.isAfter(t2));

    }


}
*/