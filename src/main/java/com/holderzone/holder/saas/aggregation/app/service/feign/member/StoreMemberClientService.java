package com.holderzone.holder.saas.aggregation.app.service.feign.member;

import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.framework.util.JacksonUtils;
import com.holderzone.framework.util.ThrowableUtils;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestSaveCMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdateMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import feign.hystrix.FallbackFactory;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@Component
@FeignClient(name = "holder-saas-store-member", fallbackFactory = StoreMemberClientService.StoreMemberClientServiceFallBack.class)
public interface StoreMemberClientService {

    @ApiOperation("根据手机号后四位查询会员")
    @GetMapping(value = "/member/query_member_by_phone_tail")
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(@RequestParam("phoneTail") String phoneTail);

    @ApiOperation("在某门店查找会员及卡信息")
    @PostMapping(value = "/member/getMemberInfoAndCard", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ResponseMemberAndCardInfoDTO getMemberInfoAndCard(@RequestBody RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO);

    @ApiOperation("修改会员基本信息")
    @PostMapping(value = "/member/updateMemberInfo/{memberInfoGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    void updateMemberInfo(@PathVariable("memberInfoGuid") String memberInfoGuid, @RequestBody RequestUpdateMemberDTO updateMemberInfoDTO);

    @ApiOperation("会员新增")
    @PostMapping(value = "/member/add", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    String add(@RequestBody RequestSaveCMemberDTO saveCMemberDTO);

    @Slf4j
    @Component
    class StoreMemberClientServiceFallBack implements FallbackFactory<StoreMemberClientService> {
        private static final String HYSTRIX_PATTERN = "服务间调用{}熔断，入参{}，异常{}";

        @Override
        public StoreMemberClientService create(Throwable throwable) {
            return new StoreMemberClientService() {
                @Override
                public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
                    log.error(HYSTRIX_PATTERN, "根据手机号后四位查询会员", phoneTail, ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public ResponseMemberAndCardInfoDTO getMemberInfoAndCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
                    log.error(HYSTRIX_PATTERN, "在某门店查找会员及卡信息", JacksonUtils.writeValueAsString(queryStoreAndMemberAndCardReqDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public void updateMemberInfo(String memberInfoGuid, RequestUpdateMemberDTO updateMemberInfoDTO) {
                    log.error(HYSTRIX_PATTERN, "修改会员基本信息", JacksonUtils.writeValueAsString(updateMemberInfoDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }

                @Override
                public String add(RequestSaveCMemberDTO saveCMemberDTO) {
                    log.error(HYSTRIX_PATTERN, "会员新增", JacksonUtils.writeValueAsString(saveCMemberDTO), ThrowableUtils.asString(throwable));
                    throw new BusinessException(throwable.getMessage());
                }
            };
        }
    }
}
