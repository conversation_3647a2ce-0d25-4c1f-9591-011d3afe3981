package com.holderzone.holder.saas.aggregation.app.controller.cmember.account;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.holderzone.efk.anno.EFKOperationLogAop;
import com.holderzone.framework.log.busines.ModuleNameType;
import com.holderzone.framework.log.busines.OperatorType;
import com.holderzone.framework.log.busines.Platform;
import com.holderzone.framework.response.Result;
import com.holderzone.holder.saas.aggregation.app.anno.RecordOperate;
import com.holderzone.holder.saas.aggregation.app.constant.Constant;
import com.holderzone.holder.saas.aggregation.app.service.feign.cmember.account.NewMemberInfoClientService;
import com.holderzone.holder.saas.aggregation.app.service.feign.member.StoreMemberClientService;
import com.holderzone.holder.saas.aggregation.app.support.TerminalMemberSupport;
import com.holderzone.holder.saas.member.terminal.dto.card.RequestMemberCardRecharge;
import com.holderzone.holder.saas.member.terminal.dto.member.request.*;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseQueryStorCardRespDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.enums.locale.LocaleMessageEnum;
import com.holderzone.saas.store.util.LocaleUtil;
import io.swagger.annotations.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @description 会员信息cotroller
 * @date 2019/6/29 10:10
 */
@Slf4j
@Api(description = "会员cotroller")
@RestController
@RequestMapping("/hsmca/member")
@RequiredArgsConstructor
public class HsaMemberInfoController {

    private final NewMemberInfoClientService memberInfoClientService;

    private final TerminalMemberSupport terminalMemberSupport;

    private final StoreMemberClientService storeMemberClientService;

    /**
     * 会员注册
     *
     * @param saveCMemberDTO
     * @return
     */
    @PostMapping("/add")
    @ApiOperation(value = "会员注册")
    @ApiResponses({@ApiResponse(code = 60011, message = "手机号已存在")})
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "会员注册", action = OperatorType.ADD)
    public Result add(@Validated @RequestBody RequestSaveCMemberDTO saveCMemberDTO) {
        String add = storeMemberClientService.add(saveCMemberDTO);
        if (add.contains("code")) {
            Result result = JSON.parseObject(add, Result.class);
            return Result.buildFailResult(result.getCode(), LocaleMessageEnum.getLocale(result.getMessage()));
        }
        return Result.buildSuccessResult(add);
    }

    /**
     * 在某门店查找会员及卡信息
     *
     * @param queryStoreAndMemberAndCardReqDTO
     * @return
     */
    @ApiOperation(value = "在某门店查找会员及卡信息", notes = "在某门店查找会员及卡信息")
    @GetMapping(value = "/getMemberInfoAndCard", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiResponses({@ApiResponse(code = 61002, message = "未注册")})
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "在某门店查找会员及卡信息", action = OperatorType.SELECT)
    public Result getMemberInfoAndCard(@Validated RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
        ResponseMemberAndCardInfoDTO memberInfoAndCard = storeMemberClientService.getMemberInfoAndCard(queryStoreAndMemberAndCardReqDTO);
        if (memberInfoAndCard != null && CollectionUtil.isNotEmpty(memberInfoAndCard.getStoreNoOpenCardListRespDTOs())) {
            memberInfoAndCard.getStoreNoOpenCardListRespDTOs().forEach(m -> {
                if (m.getCardName().equals(Constant.DEFAULT_LEVEL)) {
                    m.setCardName(LocaleUtil.getMessage("DEFAULT_LEVEL"));
                }
            });
        }
        return Result.buildSuccessResult(memberInfoAndCard);
    }


    /**
     * 在未登陆情况下，获取该门店所有体系卡信息
     */
    @GetMapping("/systemManagementCard")
    @ApiOperation(value = "获取门店所有卡信息")
    @ApiImplicitParam(value = "门店guid", required = true, paramType = "path")
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "获取莫门店所有卡信息", action = OperatorType.SELECT)
    public Result getAllSystemManagementCardByStore(RequestQueryStorCardReqDTO queryStorCardReqDTO) {
        List<ResponseQueryStorCardRespDTO> responseList = memberInfoClientService.getAllSystemManagementCardByStore(queryStorCardReqDTO);
        if (CollectionUtils.isEmpty(responseList)) {
            return Result.buildSuccessResult(new ArrayList<>());
        }
        return Result.buildSuccessResult(responseList);
    }

    /**
     * 根据 会员信息guid 和 门店查询会员基本信息和 卡开通情况
     *
     * @return
     */
    @ApiOperation(value = "根据 会员信息guid 和 门店查询会员基本信息和 卡开通情况", notes = "根据 会员信息guid 和 门店查询会员基本信息和 卡开通情况")
    @GetMapping(value = "/getMemberInfoAndCardByMemberInfoGuid")
    @ApiResponses({@ApiResponse(code = 61002, message = "未注册")})
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "根据 会员信息guid 和 门店查询会员基本信息和 卡开通情况", action = OperatorType.SELECT)
    public Result getMemberInfoAndCardByMemberInfoGuid(@ApiParam(value = "会员guid", required = true) String memberInfoGuid) {
        return Result.buildSuccessResult(memberInfoClientService.getMemberInfoAndCardByMemberInfoGuid(memberInfoGuid));
    }

    @ApiOperation(value = "会员副卡充值")
    @PostMapping(value = "/memberCardRechargeForEquityCard", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public Result memberCardRechargeForEquityCard(@Validated @RequestBody RequestMemberCardRecharge request) {
        memberInfoClientService.memberCardRechargeForEquityCard(request);
        return Result.buildEmptySuccess();
    }

    /**
     * 修改会员基本信息
     *
     * @param updateMemberInfoDTO
     * @return
     */
    @ApiOperation(value = "修改会员基本信息", notes = "修改会员基本信息")
    @ApiImplicitParam(name = "UpdateMemberInfoDTO", value = "修改会员基本信息", required = true, dataType = "UpdateMemberInfoDTO")
    @ApiResponses({@ApiResponse(code = 60011, message = "手机号已存在")})
    @PutMapping(value = "/{memberInfoGuid}", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "修改会员基本信息")
    public Result updateMemberInfo(@PathVariable("memberInfoGuid") String memberInfoGuid, @Validated @RequestBody RequestUpdateMemberDTO updateMemberInfoDTO) {
        storeMemberClientService.updateMemberInfo(memberInfoGuid, updateMemberInfoDTO);
        return Result.buildEmptySuccess();
    }

    @ApiOperation(value = "发送修改密码的验证码(忘记原密码)", notes = "发送修改密码的验证码(忘记原密码)")
    @ApiImplicitParam(name = "phoneNum", value = "手机号码", required = true, dataType = "String")
    @PostMapping(value = "/{phoneNum}/pwdCode", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "发送修改密码的验证码(忘记原密码)", action = OperatorType.SELECT)
    public Result sendUpdatePasswordCode(@ApiParam("phoneNum") @PathVariable("phoneNum") String phoneNum) {
        memberInfoClientService.sendUpdatePasswordCode(phoneNum);
        return Result.buildEmptySuccess();
    }

    /**
     * 修改会员支付密码
     *
     * @param updatePasswordReqDto
     * @return
     */
    @ApiOperation(value = "修改会员支付密码", notes = "修改会员支付密码")
    @PostMapping(value = "/updatePassword", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "修改会员支付密码")
    public Result updatePassword(@Validated @RequestBody RequestUpdatePassword updatePasswordReqDto) {
        memberInfoClientService.updatePassword(updatePasswordReqDto);
        return Result.buildEmptySuccess();
    }

    /**
     * 支付密码校验
     *
     * @param payPwd
     * @return
     */
    @ApiOperation(value = "支付密码校验", notes = "支付密码校验")
    @GetMapping(value = "/{memberInfoGuid}/payPwd/checkout", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @EFKOperationLogAop(PLATFORM = Platform.MERCHANTBACK, moduleName = ModuleNameType.HOLDER_SAAS_STORE_MEMBER, description = "支付密码校验", action = OperatorType.SELECT)
    public Result<Boolean> payPwdCheckOut(@ApiParam("会员信息guid") @PathVariable(value = "memberInfoGuid") String memberInfoGuid, @ApiParam("支付密码校验") String payPwd, @RequestParam(value = "checkExpireGuid", required = false) String checkExpireGuid) {
        return Result.buildSuccessResult(memberInfoClientService.payPwdCheckOut(memberInfoGuid, payPwd, checkExpireGuid));

    }

    @ApiOperation("根据手机号后四位查询会员")
    @GetMapping(value = "/query_member_by_phone_tail")
    public Result<List<SimpleMemberInfoDTO>> queryMemberByPhoneTail(@RequestParam("phoneTail") String phoneTail) {
        log.info("[根据手机号后四位查询会员],phoneTail={}", phoneTail);
        return Result.buildSuccessResult(storeMemberClientService.queryMemberByPhoneTail(phoneTail));
    }

}
