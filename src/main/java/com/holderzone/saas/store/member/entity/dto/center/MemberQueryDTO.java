package com.holderzone.saas.store.member.entity.dto.center;

import com.holderzone.saas.store.dto.common.BasePageDTO;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class MemberQueryDTO extends BasePageDTO {

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 电话号码
     */
    private String phoneNum;
}
