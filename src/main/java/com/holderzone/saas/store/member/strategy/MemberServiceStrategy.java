package com.holderzone.saas.store.member.strategy;

import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;

import java.util.List;

/**
 * 会员服务策略接口
 * 
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
public interface MemberServiceStrategy {
    
    /**
     * 获取策略类型
     * 
     * @return 会员服务类型
     */
    MemberServiceTypeEnum getServiceType();

    /**
     * 根据手机号后四位查询会员
     */
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail);
}
