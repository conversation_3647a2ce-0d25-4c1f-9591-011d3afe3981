package com.holderzone.saas.store.member.service;

import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestSaveCMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdateMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;

import java.util.List;


public interface MemberService {
    /**
     * 根据手机号后四位查询会员
     *
     * @param phoneTail 手机号后四位
     * @return 会员信息列表
     */
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail);

    /**
     * 在某门店查找会员及卡信息
     */
    ResponseMemberAndCardInfoDTO getMemberInfoAndCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO);

    /**
     * 修改会员基本信息
     */
    void updateMemberInfo(String memberInfoGuid, RequestUpdateMemberDTO updateMemberInfoDTO);

    /**
     * 会员新增
     *
     * @param saveCMemberDTO 会员信息
     * @return 会员guid
     */
    String add(RequestSaveCMemberDTO saveCMemberDTO);
}
