package com.holderzone.saas.store.member.controller;

import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.member.service.MemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/8/21.
 */
@RestController
@RequestMapping("/member")
@RequiredArgsConstructor
public class MemberController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(MemberController.class);

    private final MemberService memberService;

    @ApiOperation("根据手机号后四位查询会员")
    @GetMapping(value = "/query_member_by_phone_tail")
    List<SimpleMemberInfoDTO> queryMemberByPhoneTail(@RequestParam("phoneTail") String phoneTail) {
        logger.info("根据手机号号查询会员，参数：{}", phoneTail);
        return memberService.queryMemberByPhoneTail(phoneTail);
    }


}
