package com.holderzone.saas.store.member.entity.dto.center;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员信息DTO
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MemberInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 运营主体GUID
     */
    private String operSubjectGuid;

    /**
     * 企业GUID
     */
    private String enterpriseGuid;

    /**
     * 账户状态
     */
    private Integer accountState;

    /**
     * 会员GUID
     */
    private String memberGuid;

    /**
     * 会员账号
     */
    private String memberAccount;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 性别，值为1时是男性，值为2时是女性，值为0时是未知
     */
    private Integer sex;

    /**
     * 手机号
     */
    private String phoneNum;

    /**
     * 手机号区号
     */
    private String phoneCountryCode;

    /**
     * 头像
     */
    private String headImgUrl;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 会员成长值
     */
    private Integer memberGrowthValue;

    /**
     * 会员等级
     */
    private String memberLevel;

    /**
     * 会员付费等级名称
     */
    private String memberPaidLevel;

    /**
     * 会员卡
     */
    private String memberCard;

    /**
     * 会员标签
     */
    private String memberLabel;

    /**
     * 最后消费时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastConsumptionTime;

    /**
     * 消费金额
     */
    private BigDecimal consumptionMoney;

    /**
     * 消费次数
     */
    private Integer consumptionCount;

    /**
     * 充值次数
     */
    private Integer rechargeCount;

    /**
     * 充值金额
     */
    private BigDecimal rechargeMoney;

    /**
     * 会员积分
     */
    private Integer memberIntegral;

    /**
     * 总会员积分
     */
    private Integer totalMemberIntegral;

    /**
     * 账户余额
     */
    private BigDecimal accountMoney;

    /**
     * 总账户金额
     */
    private BigDecimal totalAccountMoney;

    /**
     * 注册时间
     */
    private String gmtCreate;

    /**
     * 注册渠道，80:餐饮云-微信小程序；81:餐饮云-支付宝小程序；82:餐饮云-H5；83:餐饮云-一体机；84:餐饮云-POS
     */
    private Integer sourceType;

    /**
     * 注册门店
     */
    private String belongStore;

    /**
     * 角色类型
     */
    private String roleType;

    /**
     * 联系地址
     */
    private String contactAddress;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份名称
     */
    private String provinceName;
}
