package com.holderzone.saas.store.member.entity.dto.center;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员信息DTO
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Data
@ApiModel("会员信息DTO")
public class MemberInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("运营主体GUID")
    private String operSubjectGuid;

    @ApiModelProperty("企业GUID")
    private String enterpriseGuid;

    @ApiModelProperty("账户状态")
    private Integer accountState;

    @ApiModelProperty("会员GUID")
    private String memberGuid;

    @ApiModelProperty("会员账号")
    private String memberAccount;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("性别，值为1时是男性，值为2时是女性，值为0时是未知")
    private Integer sex;

    @ApiModelProperty("手机号")
    private String phoneNum;

    @ApiModelProperty("手机号区号")
    private String phoneCountryCode;

    @ApiModelProperty("头像")
    private String headImgUrl;

    @ApiModelProperty("生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    @ApiModelProperty("会员成长值")
    private Integer memberGrowthValue;

    @ApiModelProperty("会员等级")
    private String memberLevel;

    @ApiModelProperty("会员付费等级名称")
    private String memberPaidLevel;

    @ApiModelProperty("会员卡")
    private String memberCard;

    @ApiModelProperty("会员标签")
    private String memberLabel;

    @ApiModelProperty("最后消费时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastConsumptionTime;

    @ApiModelProperty("消费金额")
    private BigDecimal consumptionMoney;

    @ApiModelProperty("消费次数")
    private Integer consumptionCount;

    @ApiModelProperty("充值次数")
    private Integer rechargeCount;

    @ApiModelProperty("充值金额")
    private BigDecimal rechargeMoney;

    @ApiModelProperty("会员积分")
    private Integer memberIntegral;

    @ApiModelProperty("总会员积分")
    private Integer totalMemberIntegral;

    @ApiModelProperty("账户余额")
    private BigDecimal accountMoney;

    @ApiModelProperty("总账户金额")
    private BigDecimal totalAccountMoney;

    @ApiModelProperty("注册时间")
    private String gmtCreate;

    @ApiModelProperty("注册渠道，80:餐饮云-微信小程序；81:餐饮云-支付宝小程序；82:餐饮云-H5；83:餐饮云-一体机；84:餐饮云-POS")
    private Integer sourceType;

    @ApiModelProperty("注册门店")
    private String belongStore;

    @ApiModelProperty("角色类型")
    private String roleType;

    @ApiModelProperty("联系地址")
    private String contactAddress;

    @ApiModelProperty("区域编码")
    private String areaCode;

    @ApiModelProperty("区域名称")
    private String areaName;
}
