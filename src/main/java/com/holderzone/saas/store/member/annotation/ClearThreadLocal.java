package com.holderzone.saas.store.member.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 清理ThreadLocal缓存注解
 * 用于标记需要在方法执行完成后清理ThreadLocal缓存的方法
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ClearThreadLocal {

}
