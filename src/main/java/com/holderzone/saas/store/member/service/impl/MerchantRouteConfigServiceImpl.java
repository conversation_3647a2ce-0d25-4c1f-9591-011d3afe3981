package com.holderzone.saas.store.member.service.impl;

import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.service.MerchantRouteConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 商户路由配置服务实现
 * 
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@Slf4j
@Service
public class MerchantRouteConfigServiceImpl implements MerchantRouteConfigService {
    
    private static final String MEMBER_SERVICE_TYPE_KEY_PREFIX = "member:service:type:";
    private static final long CACHE_EXPIRE_TIME = 24; // 缓存24小时
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public MemberServiceTypeEnum getMemberServiceType(String entGuid) {
        if (StringUtils.isBlank(entGuid)) {
            log.warn("企业GUID为空，使用默认会员中台服务");
            return MemberServiceTypeEnum.MEMBER_CENTER;
        }
        
        String entKey = MEMBER_SERVICE_TYPE_KEY_PREFIX + "ent:" + entGuid;
        String serviceType = stringRedisTemplate.opsForValue().get(entKey);
        
        if (StringUtils.isNotBlank(serviceType)) {
            log.info("根据企业配置获取会员服务类型: entGuid={}, serviceType={}", entGuid, serviceType);
            return MemberServiceTypeEnum.getByCode(serviceType);
        }
        
        // 默认使用餐饮会员服务
        log.info("未找到企业配置，使用默认餐饮会员服务: entGuid={}", entGuid);
        return MemberServiceTypeEnum.ALI_MEMBER;
    }
    
    @Override
    public Boolean updateMemberServiceType(String entGuid, MemberServiceTypeEnum serviceType) {
        if (StringUtils.isBlank(entGuid) || serviceType == null) {
            log.error("更新会员服务类型配置失败，参数不能为空: entGuid={}, serviceType={}", entGuid, serviceType);
            return false;
        }
        
        try {
            String entKey = MEMBER_SERVICE_TYPE_KEY_PREFIX + "ent:" + entGuid;
            stringRedisTemplate.opsForValue().set(entKey, serviceType.getCode(), CACHE_EXPIRE_TIME, TimeUnit.HOURS);
            log.info("更新企业会员服务类型配置成功: entGuid={}, serviceType={}", entGuid, serviceType.getCode());
            return true;
        } catch (Exception e) {
            log.error("更新企业会员服务类型配置失败: entGuid={}, serviceType={}", entGuid, serviceType.getCode(), e);
            return false;
        }
    }
}
