package com.holderzone.saas.store.member.service;

import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;

/**
 * 商户路由配置服务
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
public interface MerchantRouteConfigService {

    /**
     * 根据商户信息获取会员服务类型（仅根据运营主体Guid）
     *
     * @param operSubjectGuid 运营主体Guid
     * @return 会员服务类型
     */
    MemberServiceTypeEnum getMemberServiceType(String operSubjectGuid);

    /**
     * 更新商户的会员服务类型配置
     *
     * @param operSubjectGuid 运营主体Guid
     * @param serviceType 会员服务类型
     * @return 是否更新成功
     */
    Boolean updateMemberServiceType(String operSubjectGuid, MemberServiceTypeEnum serviceType);
}
