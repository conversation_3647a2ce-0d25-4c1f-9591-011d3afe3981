package com.holderzone.saas.store.member.service;

import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;

/**
 * 商户路由配置服务
 * 
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
public interface MerchantRouteConfigService {

    /**
     * 根据商户信息获取会员服务类型（仅根据企业GUID）
     * 
     * @param entGuid 企业GUID
     * @return 会员服务类型
     */
    MemberServiceTypeEnum getMemberServiceType(String entGuid);
    
    /**
     * 更新商户的会员服务类型配置
     * 
     * @param entGuid 企业GUID
     * @param serviceType 会员服务类型
     * @return 是否更新成功
     */
    Boolean updateMemberServiceType(String entGuid, MemberServiceTypeEnum serviceType);
}
