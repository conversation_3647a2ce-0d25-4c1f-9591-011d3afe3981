package com.holderzone.saas.store.member.aspect;

import com.holderzone.saas.store.member.annotation.ClearThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * ThreadLocal清理切面
 * 用于自动清理ThreadLocal缓存
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Aspect
@Component
@Slf4j
public class ThreadLocalClearAspect {

    /**
     * 环绕通知：在方法执行前后进行ThreadLocal清理
     */
    @Around("@annotation(com.holderzone.saas.store.member.annotation.ClearThreadLocal) || " +
            "@within(com.holderzone.saas.store.member.annotation.ClearThreadLocal)")
    public Object clearThreadLocal(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } finally {
            // 获取注解配置
            ClearThreadLocal annotation = getAnnotation(joinPoint);
            if (annotation != null) {
                clearThreadLocalCaches(annotation);
            }
        }
    }

    /**
     * 清理ThreadLocal缓存
     *
     * 清理原因：
     * 1. 防止内存泄漏：ThreadLocal变量在线程生命周期内持续存在
     * 2. 避免线程池复用时的数据污染：Web容器通常使用线程池，线程会被复用
     * 3. 确保运营主体上下文隔离：防止不同请求间的权限和数据混乱
     * 4. 保证链路追踪的准确性：避免traceId在不同请求间串联
     */
    private void clearThreadLocalCaches(ClearThreadLocal annotation) {
        try {
            if (annotation.clearCache()) {
                // 清理通用缓存，防止缓存数据在线程复用时污染后续请求
                clearThreadLocalCache();
                log.debug("ThreadLocalCache cleared");
            }

            if (annotation.clearTraceId()) {
                // 清理链路追踪ID，确保每个请求的traceId独立
                clearTraceidUtils();
                log.debug("TraceidUtils cleared");
            }

            if (annotation.clearOperSubject()) {
                // 清理运营主体缓存，防止权限和数据越权访问
                clearThreadLocalOperSubjectCache();
                log.debug("ThreadLocalOperSubjectCache cleared");
            }

            log.debug("ThreadLocal缓存清理完成");
        } catch (Exception e) {
            log.error("清理ThreadLocal缓存时发生异常", e);
        }
    }

    /**
     * 获取注解
     */
    private ClearThreadLocal getAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 先检查方法级别的注解
        ClearThreadLocal annotation = method.getAnnotation(ClearThreadLocal.class);
        if (annotation != null) {
            return annotation;
        }

        // 再检查类级别的注解
        return joinPoint.getTarget().getClass().getAnnotation(ClearThreadLocal.class);
    }

    /**
     * 清理ThreadLocalCache
     */
    private void clearThreadLocalCache() {
        try {
            Class<?> clazz = Class.forName("com.holderzone.framework.ThreadLocalCache");
            Method removeMethod = clazz.getMethod("remove");
            removeMethod.invoke(null);
        } catch (Exception e) {
            log.warn("清理ThreadLocalCache失败", e);
        }
    }

    /**
     * 清理TraceidUtils
     */
    private void clearTraceidUtils() {
        try {
            Class<?> clazz = Class.forName("com.holderzone.framework.TraceidUtils");
            Method clearMethod = clazz.getMethod("clear");
            clearMethod.invoke(null);
        } catch (Exception e) {
            log.warn("清理TraceidUtils失败", e);
        }
    }

    /**
     * 清理ThreadLocalOperSubjectCache
     *
     * 为什么要清理ThreadLocalOperSubjectCache：
     * 1. 防止内存泄漏：ThreadLocal持有的对象无法被GC回收
     * 2. 避免数据污染：线程池复用时，旧的运营主体信息会影响新请求
     * 3. 权限隔离：确保不同运营主体的数据和权限不会混乱
     * 4. 数据安全：防止敏感的运营主体信息泄露到其他请求
     */
    private void clearThreadLocalOperSubjectCache() {
        try {
            Class<?> clazz = Class.forName("com.holderzone.framework.ThreadLocalOperSubjectCache");
            Method removeMethod = clazz.getMethod("remove");
            removeMethod.invoke(null);
        } catch (Exception e) {
            log.warn("清理ThreadLocalOperSubjectCache失败", e);
        }
    }

}
