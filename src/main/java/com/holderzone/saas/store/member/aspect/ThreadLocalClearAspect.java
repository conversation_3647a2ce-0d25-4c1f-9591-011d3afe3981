package com.holderzone.saas.store.member.aspect;

import com.holderzone.saas.store.member.annotation.ClearThreadLocal;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * ThreadLocal清理切面
 * 用于自动清理ThreadLocal缓存
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Aspect
@Component
@Slf4j
public class ThreadLocalClearAspect {

    /**
     * 环绕通知：在方法执行前后进行ThreadLocal清理
     */
    @Around("@annotation(com.holderzone.saas.store.member.annotation.ClearThreadLocal) || " +
            "@within(com.holderzone.saas.store.member.annotation.ClearThreadLocal)")
    public Object clearThreadLocal(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } finally {
            // 获取注解配置
            ClearThreadLocal annotation = getAnnotation(joinPoint);
            if (annotation != null) {
                clearThreadLocalCaches(annotation);
            }
        }
    }

    /**
     * 获取注解
     */
    private ClearThreadLocal getAnnotation(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 先检查方法级别的注解
        ClearThreadLocal annotation = method.getAnnotation(ClearThreadLocal.class);
        if (annotation != null) {
            return annotation;
        }
        
        // 再检查类级别的注解
        return joinPoint.getTarget().getClass().getAnnotation(ClearThreadLocal.class);
    }

    /**
     * 清理ThreadLocal缓存
     */
    private void clearThreadLocalCaches(ClearThreadLocal annotation) {
        try {
            if (annotation.clearCache()) {
                // ThreadLocalCache.remove();
                clearThreadLocalCache();
            }
            
            if (annotation.clearTraceId()) {
                // TraceidUtils.clear();
                clearTraceidUtils();
            }
            
            if (annotation.clearOperSubject()) {
                // ThreadLocalOperSubjectCache.remove();
                clearThreadLocalOperSubjectCache();
            }
            
            log.debug("ThreadLocal缓存清理完成");
        } catch (Exception e) {
            log.error("清理ThreadLocal缓存时发生异常", e);
        }
    }

    /**
     * 清理ThreadLocalCache
     */
    private void clearThreadLocalCache() {
        try {
            // 使用反射调用ThreadLocalCache.remove()
            Class<?> clazz = Class.forName("com.holderzone.framework.ThreadLocalCache");
            Method removeMethod = clazz.getMethod("remove");
            removeMethod.invoke(null);
        } catch (Exception e) {
            log.warn("清理ThreadLocalCache失败", e);
        }
    }

    /**
     * 清理TraceidUtils
     */
    private void clearTraceidUtils() {
        try {
            // 使用反射调用TraceidUtils.clear()
            Class<?> clazz = Class.forName("com.holderzone.framework.TraceidUtils");
            Method clearMethod = clazz.getMethod("clear");
            clearMethod.invoke(null);
        } catch (Exception e) {
            log.warn("清理TraceidUtils失败", e);
        }
    }

    /**
     * 清理ThreadLocalOperSubjectCache
     */
    private void clearThreadLocalOperSubjectCache() {
        try {
            // 使用反射调用ThreadLocalOperSubjectCache.remove()
            Class<?> clazz = Class.forName("com.holderzone.framework.ThreadLocalOperSubjectCache");
            Method removeMethod = clazz.getMethod("remove");
            removeMethod.invoke(null);
        } catch (Exception e) {
            log.warn("清理ThreadLocalOperSubjectCache失败", e);
        }
    }
}
