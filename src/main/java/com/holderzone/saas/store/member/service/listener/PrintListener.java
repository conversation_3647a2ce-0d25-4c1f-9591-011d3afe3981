package com.holderzone.saas.store.member.service.listener;

import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.anno.CustomerRegister;
import com.holderzone.framework.event.CustomerEvent;
import com.holderzone.framework.event.observer.CustomerObserver;
import com.holderzone.framework.util.DateTimeUtils;
import com.holderzone.saas.store.dto.member.MemberNotifyDTO;
import com.holderzone.saas.store.dto.organization.StoreDTO;
import com.holderzone.saas.store.dto.print.content.PrintStoredCashDTO;
import com.holderzone.saas.store.dto.trade.BaseInfo;
import com.holderzone.saas.store.enums.print.InvoiceTypeEnum;
import com.holderzone.saas.store.enums.print.PrintSourceEnum;
import com.holderzone.saas.store.member.entity.bo.MemberPayBO;
import com.holderzone.saas.store.member.entity.bo.TransactionPrintBO;
import com.holderzone.saas.store.member.service.MemberService;
import com.holderzone.saas.store.member.service.rpc.EntServiceClient;
import com.holderzone.saas.store.member.service.rpc.MemberStoreService;
import com.holderzone.saas.store.member.service.rpc.MemberTransactionPrintService;
import com.holderzone.saas.store.member.service.rpc.MsgClientService;
import com.holderzone.saas.store.member.utils.DynamicHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * <AUTHOR>
 * @version 1.0
 * @className PrintListener
 * @date 2018/11/05 11:00
 * @description 打印及发送短信异步执行
 * @program holder-saas-member
 */
@Component
@CustomerRegister(isRegister = true)
public class PrintListener implements CustomerObserver<MemberPayBO> {

    private static final Logger logger = LoggerFactory.getLogger(PrintListener.class);

    private final MemberStoreService memberStoreService;
    private final MemberTransactionPrintService memberTransactionPrintService;
    private final MemberService memberService;
    private final EntServiceClient es;
    private final MsgClientService ms;

    @Autowired
    public PrintListener(MemberStoreService memberStoreService, MemberTransactionPrintService
            memberTransactionPrintService, MemberService memberService,
                         EntServiceClient es, MsgClientService ms
    ) {

        this.memberTransactionPrintService = memberTransactionPrintService;
        this.memberStoreService = memberStoreService;
        this.memberService = memberService;
        this.es = es;
        this.ms = ms;
    }

    @Autowired
    private DynamicHelper helper;

    @Autowired
    private DynamicHelper c1;


    @Override
    public void notify(CustomerEvent<MemberPayBO> customerEvent) {
        PrintStoredCashDTO st = new PrintStoredCashDTO();
        MemberPayBO memberPayBO = (MemberPayBO) customerEvent.getSource();
        helper.changeDatasource(memberPayBO.getEntGuid());
        helper.changeRedis(memberPayBO.getEntGuid());
        BaseInfo bs = memberPayBO.getBs();
        MemberNotifyDTO memberNotifyDTO = memberPayBO.getMemberNotifyDTO();
        TransactionPrintBO transactionPrintBO = memberPayBO.getTransactionPrintBO();
        //充值金额
            st.setArrival(memberNotifyDTO.getAmount());
            st.setRecharge(memberNotifyDTO.getAmount());
            //交易流水号
            st.setSerialNumber(memberNotifyDTO.getSequenceNo());
            //赠送金额
            st.setPresented(new BigDecimal(0));
            //到账金额
            st.setArrival(st.getPresented().add(memberNotifyDTO.getAmount()));
            //充值方式
            st.setPayWay(memberNotifyDTO.getPrepaidTypeName());
            //TODO 充值卡号(通过会员Guid sql查询电子卡号)
//            st.setCardNo(memberService.getMemberCard(memberNotifyDTO.getMemberGuid()).getNum());
            //当前余额(通过Guid查询账户余额)
            st.setCurrentCash(transactionPrintBO.getBalance());
            //当前积分
            st.setIntegration(String.valueOf(transactionPrintBO.getResidualintegral()));
            //充值时间

            st.setRechargeTime(getTimestampOfDateTime(transactionPrintBO.getPrepaidTime()));
            //打印时间
            st.setCreateTime(getTimestampOfDateTime(LocalDateTime.now()));
            //门店信息
            UserContextUtils.putErp(memberPayBO.getEntGuid());
            StoreDTO storeDTO = memberStoreService.queryStoreByGuid(bs.getStoreGuid());

            String provinceName = storeDTO.getProvinceName() == null ? "" : storeDTO
                    .getProvinceName();
            String cityName = storeDTO.getCityName() == null ? "" : storeDTO.getCityName();
            String address = storeDTO.getAddressDetail() == null ? "" : storeDTO.getAddressDetail();
            st.setStoreAddress(provinceName + cityName + address);
            st.setTel(storeDTO.getContactTel());
            st.setStoreName(storeDTO.getName());
            st.setStoreGuid(storeDTO.getGuid());
            st.setEnterpriseGuid(memberNotifyDTO.getEnterpriseGuid());
            //操作人
            st.setOperatorStaffGuid(memberNotifyDTO.getOperationStaffGuid());
            st.setOperatorStaffName(memberNotifyDTO.getOperationStaffName());
            st.setInvoiceType(InvoiceTypeEnum.STORED_CASH.getType());
            st.setSerialNumber(memberNotifyDTO.getSequenceNo());
            st.setCreateTime(DateTimeUtils.nowMillis());
            // fixme 删除mock的deviceId,使用从memberNotifyDTO里面解析的Deviceid
            st.setDeviceId(memberNotifyDTO.getDeviceId() == null ? bs.getDeviceId() : memberNotifyDTO.getDeviceId());
            st.setPrintUid(memberNotifyDTO.getSequenceNo());
            //打印来源
            st.setPrintSourceEnum(PrintSourceEnum.getPrintSourceByDeviceType(bs.getDeviceType()));
            memberTransactionPrintService.printMemberTransactionLog(st);
        c1.removeThreadLocalRedisInfo();
        c1.removeThreadLocalDatabaseInfo();
        UserContextUtils.remove();
    }

    public static long getTimestampOfDateTime(LocalDateTime localDateTime) {
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDateTime.atZone(zone).toInstant();
        return instant.toEpochMilli();
    }
}
