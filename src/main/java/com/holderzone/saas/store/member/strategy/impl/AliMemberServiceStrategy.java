package com.holderzone.saas.store.member.strategy.impl;

import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.service.rpc.AliMemberClientService;
import com.holderzone.saas.store.member.strategy.MemberServiceStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 餐饮会员服务策略实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AliMemberServiceStrategy implements MemberServiceStrategy {

    private final AliMemberClientService aliMemberClientService;

    @Override
    public MemberServiceTypeEnum getServiceType() {
        return MemberServiceTypeEnum.ALI_MEMBER;
    }

    @Override
    public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
        log.info("使用餐饮会员服务根据手机号后四位查询会员: {}", phoneTail);
        return aliMemberClientService.queryMemberByPhoneTail(phoneTail);
    }
}
