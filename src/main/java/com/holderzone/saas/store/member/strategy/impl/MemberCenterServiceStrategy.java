package com.holderzone.saas.store.member.strategy.impl;

import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.strategy.MemberServiceStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 会员中台服务策略实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@Slf4j
@Component
public class MemberCenterServiceStrategy implements MemberServiceStrategy {

    // TODO: 注入会员中台相关的服务或客户端

    @Override
    public MemberServiceTypeEnum getServiceType() {
        return MemberServiceTypeEnum.MEMBER_CENTER;
    }

    @Override
    public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
        log.info("使用会员中台服务根据手机号后四位查询会员: {}", phoneTail);
        // TODO: 调用会员中台服务的查询会员接口
        throw new UnsupportedOperationException("会员中台服务根据手机号后四位查询会员功能待实现");
    }
}
