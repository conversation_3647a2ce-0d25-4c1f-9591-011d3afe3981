package com.holderzone.saas.store.member.strategy.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.holderzone.feign.spring.boot.util.UserContextUtils;
import com.holderzone.framework.exception.unchecked.BusinessException;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestQueryStoreAndMemberAndCard;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestSaveCMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.request.RequestUpdateMemberDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberAndCardInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberCard;
import com.holderzone.holder.saas.member.terminal.dto.member.response.ResponseMemberInfoDTO;
import com.holderzone.holder.saas.member.terminal.dto.member.response.SimpleMemberInfoDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmAggPayRespDTO;
import com.holderzone.saas.store.dto.member.hsm.HsmRechargeReqDTO;
import com.holderzone.saas.store.member.annotation.ClearThreadLocal;
import com.holderzone.saas.store.member.entity.constant.center.MemberCenterApiConstant;
import com.holderzone.saas.store.member.entity.constant.center.MemberSourceTypeEnum;
import com.holderzone.saas.store.member.entity.dto.center.*;
import com.holderzone.saas.store.member.enums.MemberServiceTypeEnum;
import com.holderzone.saas.store.member.strategy.MemberServiceStrategy;
import com.holderzone.saas.store.member.service.rpc.MemberCenterClientService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 会员中台服务策略实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/8
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ClearThreadLocal
public class MemberCenterServiceStrategy implements MemberServiceStrategy {

    private final MemberCenterClientService clientService;

    @Override
    public MemberServiceTypeEnum getServiceType() {
        return MemberServiceTypeEnum.MEMBER_CENTER;
    }

    @Override
    public List<SimpleMemberInfoDTO> queryMemberByPhoneTail(String phoneTail) {
        log.info("使用会员中台服务根据手机号后四位查询会员: {}", phoneTail);
        List<MemberInfoDTO> members = clientService.executeMemberGetRequestList(MemberCenterApiConstant.GET_MEMBERS_BY_PHONE,
                MemberQueryDTO.builder().phoneNum(phoneTail).build(),
                MemberInfoDTO.class);
        if (CollectionUtil.isEmpty(members)) {
            return CollectionUtil.newArrayList();
        }
        List<SimpleMemberInfoDTO> simpleMemberInfoDTOS = new ArrayList<>();
        members.forEach(member -> {
            SimpleMemberInfoDTO simpleMemberInfoDTO = new SimpleMemberInfoDTO();
            simpleMemberInfoDTO.setMemberInfoGuid(member.getMemberGuid());
            simpleMemberInfoDTO.setNickName(member.getUserName());
            simpleMemberInfoDTO.setPhoneNum(member.getPhoneNum());
            simpleMemberInfoDTO.setOperSubjectGuid(member.getOperSubjectGuid());
            simpleMemberInfoDTOS.add(simpleMemberInfoDTO);
        });
        return simpleMemberInfoDTOS;
    }

    @Override
    public ResponseMemberAndCardInfoDTO getMemberInfoAndCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO) {
        ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO = new ResponseMemberAndCardInfoDTO();
        //查询会员个人信息
        getMemberInfo(queryStoreAndMemberAndCardReqDTO, responseMemberAndCardInfoDTO);
        //查询会员卡信息
        getMemberCard(queryStoreAndMemberAndCardReqDTO, responseMemberAndCardInfoDTO);
        //查询未开通的会员卡信息
        getNotOpenMemberCard(queryStoreAndMemberAndCardReqDTO, responseMemberAndCardInfoDTO);
        return responseMemberAndCardInfoDTO;
    }

    @Override
    public void updateMemberInfo(String memberInfoGuid, RequestUpdateMemberDTO updateMemberInfoDTO) {
        MemberInfoDTO memberInfoDTO = MemberInfoDTO.builder().memberGuid(memberInfoGuid).userName(updateMemberInfoDTO.getNickName())
                .sex(updateMemberInfoDTO.getSex()).birthday(updateMemberInfoDTO.getBirthday())
                .provinceCode(updateMemberInfoDTO.getProvinceCode()).provinceName(updateMemberInfoDTO.getProvinceName())
                .cityCode(updateMemberInfoDTO.getCityCode()).cityName(updateMemberInfoDTO.getCityName())
                .areaCode(updateMemberInfoDTO.getAreaCode()).areaName(updateMemberInfoDTO.getAreaName())
                .contactAddress(updateMemberInfoDTO.getAddress()).build();
        Boolean success = clientService.executeMemberRequestBoolean(MemberCenterApiConstant.UPDATE_MEMBER_INFO, memberInfoDTO);
        if (!success) {
            throw new BusinessException("修改会员信息失败");
        }
    }

    @Override
    public String add(RequestSaveCMemberDTO saveCMemberDTO) {
        //会员卡信息
        List<MemberCardInfoDTO> memberCardInfoDTOS = new ArrayList<>();
        memberCardInfoDTOS.add(MemberCardInfoDTO.builder().cardGuid(saveCMemberDTO.getCardGuid())
                .enterpriseGuid(saveCMemberDTO.getEnterpriseGuid())
                .operSubjectGuid(UserContextUtils.get().getOperSubjectGuid())
                .source(MemberSourceTypeEnum.convertToMemberCenterCode(saveCMemberDTO.getSourceType()))
                .build());
        //会员信息
        MemberInfoDTO memberRequest = MemberInfoDTO.builder()
                .phoneNum(saveCMemberDTO.getPhoneNum()).userName(saveCMemberDTO.getNickName())
                .sex(saveCMemberDTO.getSex()).birthday(saveCMemberDTO.getBirthday())
                .provinceCode(saveCMemberDTO.getProvinceName()).provinceName(saveCMemberDTO.getProvinceName())
                .cityCode(saveCMemberDTO.getCityCode()).cityName(saveCMemberDTO.getCityName())
                .areaCode(saveCMemberDTO.getAreaCode()).areaName(saveCMemberDTO.getAreaName())
                .contactAddress(saveCMemberDTO.getAddress())
                .belongStore(saveCMemberDTO.getStoreGuid())
                .cardOpenFlag(Boolean.TRUE).cardOpenList(memberCardInfoDTOS)
                .build();
        MemberInfoDTO memberInfoDTO = clientService.executeMemberRequest(MemberCenterApiConstant.ADD_MEMBER, memberRequest, MemberInfoDTO.class);
        if (memberInfoDTO == null) {
            throw new BusinessException("会员新增失败");
        }
        return memberInfoDTO.getMemberGuid();
    }

    @Override
    public HsmAggPayRespDTO recharge(HsmRechargeReqDTO hsmRechargeReqDTO) {
        //现金支付
        if (hsmRechargeReqDTO.getPayWay() == 0) {
            return cashRecharge(hsmRechargeReqDTO);
        }
        return null;
    }

    /**
     * 现金充值
     */
    private HsmAggPayRespDTO cashRecharge(HsmRechargeReqDTO hsmRechargeReqDTO) {
        MemberRechargeDTO rechargeDTO = MemberRechargeDTO.builder().memberInfoCardGuid(hsmRechargeReqDTO.getMemberInfoCardGuid())
                .memberInfoGuid(hsmRechargeReqDTO.getMemberGuid())
                .rechargeMoney(hsmRechargeReqDTO.getRechargeMoney())
                .storeGuid(hsmRechargeReqDTO.getStoreGuid())
                .storeName(hsmRechargeReqDTO.getStoreName()).build();
        MemberRechargeResponseDTO memberRechargeResponseDTO = clientService.executeMemberRequest(MemberCenterApiConstant.CARD_RECHARGE, rechargeDTO, MemberRechargeResponseDTO.class);
        HsmAggPayRespDTO hsmAggPayRespDTO = new HsmAggPayRespDTO();
        if (memberRechargeResponseDTO != null) {
            hsmAggPayRespDTO.setResult(Boolean.TRUE);
            return hsmAggPayRespDTO;
        }
        hsmAggPayRespDTO.setResult(Boolean.FALSE);
        return hsmAggPayRespDTO;
    }

    /**
     * 获取未开通的会员卡信息
     */
    private void getNotOpenMemberCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO, ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO) {
        List<MemberCardInfoDTO> memberCardInfoDTOS = clientService.executeMemberRequestList(MemberCenterApiConstant.GET_MEMBER_CARD,
                MemberCardQueryDTO.builder().memberInfoGuid(queryStoreAndMemberAndCardReqDTO.getMemberGuid()).type(2).storeGuid(queryStoreAndMemberAndCardReqDTO.getStoreGuid()).build(),
                MemberCardInfoDTO.class);
        if (CollectionUtil.isEmpty(memberCardInfoDTOS)) {
            return;
        }
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(getResponseMemberCards(memberCardInfoDTOS));
    }

    /**
     * 获取已开通会员卡信息
     */
    private void getMemberCard(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO, ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO) {
        List<MemberCardInfoDTO> memberCardInfoDTOS = clientService.executeMemberRequestList(MemberCenterApiConstant.GET_MEMBER_CARD,
                MemberCardQueryDTO.builder().memberInfoGuid(queryStoreAndMemberAndCardReqDTO.getMemberGuid()).type(0).storeGuid(queryStoreAndMemberAndCardReqDTO.getStoreGuid()).build(),
                MemberCardInfoDTO.class);
        if (CollectionUtil.isEmpty(memberCardInfoDTOS)) {
            return;
        }
        responseMemberAndCardInfoDTO.setMemberCardListRespDTOs(getResponseMemberCards(memberCardInfoDTOS));
    }

    /**
     * 会员卡信息转换
     */
    private @NotNull List<ResponseMemberCard> getResponseMemberCards(List<MemberCardInfoDTO> memberCardInfoDTOS) {
        List<ResponseMemberCard> responseMemberCards = new ArrayList<>();
        memberCardInfoDTOS.forEach(memberCardInfoDTO -> {
            ResponseMemberCard responseMemberCard = new ResponseMemberCard();
            responseMemberCard.setCardGuid(memberCardInfoDTO.getCardGuid());
            responseMemberCard.setCardMoney(memberCardInfoDTO.getAccountMoney());
            responseMemberCard.setCardName(memberCardInfoDTO.getCardName());
            //卡类型不一致
            responseMemberCard.setCardType(memberCardInfoDTO.getCardType());
            responseMemberCard.setFreezeMoney(memberCardInfoDTO.getFreezeAmount());
            responseMemberCard.setMemberInfoCardGuid(memberCardInfoDTO.getOwnGuid());
            responseMemberCards.add(responseMemberCard);
        });
        return responseMemberCards;
    }

    /**
     * 获取会员信息
     */
    private void getMemberInfo(RequestQueryStoreAndMemberAndCard queryStoreAndMemberAndCardReqDTO, ResponseMemberAndCardInfoDTO responseMemberAndCardInfoDTO) {
        MemberInfoDTO memberInfoDto = clientService.executeMemberRequest(MemberCenterApiConstant.GET_MEMBER_INFO,
                MemberQueryDTO.builder().memberGuid(queryStoreAndMemberAndCardReqDTO.getMemberGuid()).phoneNum(queryStoreAndMemberAndCardReqDTO.getPhoneNumOrCardNum()).build(),
                MemberInfoDTO.class);
        if (memberInfoDto != null) {
            ResponseMemberInfoDTO responseMemberInfoDTO = new ResponseMemberInfoDTO();
            responseMemberInfoDTO.setAddress(memberInfoDto.getContactAddress());
            responseMemberInfoDTO.setAreaCode(memberInfoDto.getAreaCode());
            responseMemberInfoDTO.setAreaName(memberInfoDto.getAreaName());
            responseMemberInfoDTO.setBirthday(memberInfoDto.getBirthday());
            responseMemberInfoDTO.setMemberInfoGuid(memberInfoDto.getMemberGuid());
            responseMemberInfoDTO.setGmtCreate(memberInfoDto.getGmtCreate());
            responseMemberInfoDTO.setHeadImgUrl(memberInfoDto.getHeadImgUrl());
            responseMemberInfoDTO.setNickName(memberInfoDto.getUserName());
            responseMemberInfoDTO.setPhoneNum(memberInfoDto.getPhoneNum());
            responseMemberInfoDTO.setSex(memberInfoDto.getSex());
            responseMemberInfoDTO.setMemberStoreName(memberInfoDto.getBelongStore());
            //会员中台sourceType需要进行转换
            responseMemberInfoDTO.setSourceType(MemberSourceTypeEnum.convertToStoreMemberSourceCode(memberInfoDto.getSourceType()));
            responseMemberInfoDTO.setLastConsumptionTime(memberInfoDto.getLastConsumptionTime());
            responseMemberAndCardInfoDTO.setMemberInfoDTO(responseMemberInfoDTO);
        }
    }
}
