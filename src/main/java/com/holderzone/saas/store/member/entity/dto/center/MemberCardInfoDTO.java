package com.holderzone.saas.store.member.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 会员卡信息DTO
 *
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Data
@ApiModel("会员卡信息DTO")
public class MemberCardInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("会员持卡GUID，hsa_member_info_card guid")
    private String ownGuid;

    @ApiModelProperty("会员卡GUID")
    private String cardGuid;

    @ApiModelProperty("会员卡名称")
    private String cardName;

    @ApiModelProperty("会员卡类型 0 实体卡 1电子卡")
    private Integer cardType;

    @ApiModelProperty("卡颜色")
    private String cardColor;

    @ApiModelProperty("冻结金额")
    private BigDecimal freezeAmount;

    @ApiModelProperty("卡图片")
    private String cardImage;

    @ApiModelProperty("二维码")
    private String qrCode;

    @ApiModelProperty("卡面值金额")
    private BigDecimal cardValueMoney;

    @ApiModelProperty("卡状态")
    private Integer cardStatus;

    @ApiModelProperty("卡状态")
    private Integer cardState;

    @ApiModelProperty("实体卡状态")
    private Integer physicalCardState;

    @ApiModelProperty("开卡日期")
    private String gmtCreate;

    @ApiModelProperty("卡有效期")
    private Integer cardValidity;

    @ApiModelProperty("卡有效期时间")
    private String cardValidityTime;

    @ApiModelProperty("卡有效期日期")
    private String cardValidityDate;

    @ApiModelProperty("有效期单位")
    private String validityUnit;

    @ApiModelProperty("账户余额")
    private BigDecimal accountMoney;

    @ApiModelProperty("开卡时间类型")
    private Integer openCardTimeType;

    @ApiModelProperty("默认卡")
    private Integer defaultCard;

    @ApiModelProperty("会员持卡GUID")
    private String memberInfoCardGuid;

    @ApiModelProperty("自助类型")
    private Integer selfType;

    @ApiModelProperty("自助支付金额")
    private BigDecimal selfPaymentMoney;

    @ApiModelProperty("自助充值金额")
    private BigDecimal selfRechargeMoney;

    @ApiModelProperty("发送状态")
    private Integer sendStatus;

    @ApiModelProperty("电子卡开通方式")
    private Integer electronicOpenWay;

    @ApiModelProperty("应付金额")
    private BigDecimal shouldPayMoney;

    @ApiModelProperty("优惠金额")
    private BigDecimal economyMoney;

    @ApiModelProperty("绑定门店数量")
    private Integer bindingStoreCount;

    @ApiModelProperty("卡使用说明")
    private String cardEmployExplain;

    @ApiModelProperty("有效时间段")
    private String periodOfValidity;

    @ApiModelProperty("会员卡卡号")
    private String electronicCardNum;

    @ApiModelProperty("门店数量")
    private Integer storeNum;

    @ApiModelProperty("适用所有门店")
    private Integer applicableAllStore;

    @ApiModelProperty("是否支持电子卡")
    private Integer isSupportElectronicCard;

    @ApiModelProperty("是否需要密码")
    private Integer isPassword;

    @ApiModelProperty("是否支持超额")
    private Integer isExcess;

    @ApiModelProperty("超额类型 0 次数 1 金额")
    private Integer excessType;

    @ApiModelProperty("超额金额")
    private BigDecimal excessAmount;

    @ApiModelProperty("超额次数")
    private Integer excessTimes;

    @ApiModelProperty("是否适用当前门店")
    private Boolean isUsableStore;

    @ApiModelProperty("是否预存")
    private Integer isPreStored;

    @ApiModelProperty("小程序充值")
    private Integer appletRecharge;

    @ApiModelProperty("充值提示")
    private String rechargeTips;

    @ApiModelProperty("等级权益列表")
    private Object gradeEquitiesVOList;
}
