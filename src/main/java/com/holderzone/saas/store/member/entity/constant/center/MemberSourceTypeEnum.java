package com.holderzone.saas.store.member.entity.constant.center;

import lombok.Getter;

/**
 * <AUTHOR> href="mailto:<EMAIL>">xieyingliang</a>
 * @date 2025/8/10
 */
@Getter
public enum MemberSourceTypeEnum {

    /**
     * 餐饮云-微信小程序
     */
    WECHAT_MINI_PROGRAM(80, "餐饮云-微信小程序"),

    /**
     * 餐饮云-支付宝小程序
     */
    ALIPAY_MINI_PROGRAM(81, "餐饮云-支付宝小程序"),

    /**
     * 餐饮云-H5
     */
    H5(82, "餐饮云-H5"),

    /**
     * 餐饮云-一体机
     */
    ALL_IN_ONE_MACHINE(83, "餐饮云-一体机"),

    /**
     * 餐饮云-POS
     */
    POS(84, "餐饮云-POS"),

    /**
     * 餐饮云-PAD
     */
    PAD(85, "餐饮云-PAD");

    /**
     * 源类型代码
     */
    private final Integer code;

    /**
     * 源类型描述
     */
    private final String description;

    MemberSourceTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 源类型代码
     * @return 对应的枚举值，如果未找到返回null
     */
    public static MemberSourceTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (MemberSourceTypeEnum sourceType : values()) {
            if (sourceType.getCode().equals(code)) {
                return sourceType;
            }
        }
        return null;
    }

    /**
     * 根据代码获取描述
     *
     * @param code 源类型代码
     * @return 对应的描述，如果未找到返回null
     */
    public static String getDescriptionByCode(Integer code) {
        MemberSourceTypeEnum sourceType = getByCode(code);
        return sourceType != null ? sourceType.getDescription() : null;
    }
}
